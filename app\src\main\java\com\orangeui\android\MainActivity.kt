package com.orangeui.android

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.orangeui.android.ui.activities.ProfileActivity
import com.orangeui.android.ui.activities.SettingsActivity
import com.orangeui.android.ui.components.OrangeCard
import com.orangeui.android.ui.components.OrangeButton
import com.orangeui.android.ui.theme.OrangeUITheme
import com.orangeui.android.native.NativeLibrary
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    
    private val nativeLibrary = NativeLibrary()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Initialize native library
        nativeLibrary.initialize()
        
        setContent {
            OrangeUITheme {
                MainScreen(
                    onNavigateToProfile = {
                        startActivity(Intent(this, ProfileActivity::class.java))
                    },
                    onNavigateToSettings = {
                        startActivity(Intent(this, SettingsActivity::class.java))
                    },
                    nativeLibrary = nativeLibrary
                )
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        nativeLibrary.cleanup()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onNavigateToProfile: () -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    nativeLibrary: NativeLibrary? = null
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    var showMenu by remember { mutableStateOf(false) }
    var nativeMessage by remember { mutableStateOf("Loading...") }
    
    // Get message from native library
    LaunchedEffect(nativeLibrary) {
        nativeLibrary?.let {
            nativeMessage = it.getWelcomeMessage()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.app_name),
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                },
                actions = {
                    IconButton(onClick = { showMenu = !showMenu }) {
                        Icon(
                            Icons.Default.Menu,
                            contentDescription = stringResource(R.string.cd_menu_button),
                            tint = Color.White
                        )
                    }
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.nav_profile)) },
                            onClick = {
                                showMenu = false
                                onNavigateToProfile()
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Person, contentDescription = null)
                            }
                        )
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.nav_settings)) },
                            onClick = {
                                showMenu = false
                                onNavigateToSettings()
                            },
                            leadingIcon = {
                                Icon(Icons.Default.Settings, contentDescription = null)
                            }
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFFF6B35)
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                WelcomeSection(nativeMessage)
            }
            
            item {
                QuickActionsSection(
                    onProfileClick = onNavigateToProfile,
                    onSettingsClick = onNavigateToSettings
                )
            }
            
            items(getDemoFeatures()) { feature ->
                FeatureCard(feature)
            }
        }
    }
}

@Composable
fun WelcomeSection(nativeMessage: String) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFFFF6B35),
                            Color(0xFFFF9500)
                        )
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.welcome_message),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(R.string.subtitle_message),
                    fontSize = 16.sp,
                    color = Color.White.copy(alpha = 0.9f)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = nativeMessage,
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }
        }
    }
}

@Composable
fun QuickActionsSection(
    onProfileClick: () -> Unit,
    onSettingsClick: () -> Unit
) {
    OrangeCard(
        title = "Quick Actions",
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            OrangeButton(
                text = stringResource(R.string.nav_profile),
                onClick = onProfileClick,
                modifier = Modifier.weight(1f)
            )
            Spacer(modifier = Modifier.width(16.dp))
            OrangeButton(
                text = stringResource(R.string.nav_settings),
                onClick = onSettingsClick,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
fun FeatureCard(feature: DemoFeature) {
    OrangeCard(
        title = feature.title,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = feature.description,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

data class DemoFeature(
    val title: String,
    val description: String
)

fun getDemoFeatures(): List<DemoFeature> {
    return listOf(
        DemoFeature(
            "Native Performance",
            "Powered by C/C++ native libraries for optimal performance"
        ),
        DemoFeature(
            "Rust Security",
            "Enhanced security features implemented in Rust"
        ),
        DemoFeature(
            "Modern UI",
            "Beautiful Material Design 3 with Orange UI theme"
        ),
        DemoFeature(
            "Android 16 Ready",
            "Built for the latest Android 16 features and APIs"
        )
    )
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    OrangeUITheme {
        MainScreen()
    }
}
