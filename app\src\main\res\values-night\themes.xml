<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Orange UI Dark Theme -->
    <style name="Theme.OrangeUI" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/orange_primary_light</item>
        <item name="colorPrimaryVariant">@color/orange_primary</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/orange_secondary_light</item>
        <item name="colorSecondaryVariant">@color/orange_secondary</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/orange_background_dark</item>
        <item name="colorSurface">@color/orange_surface_dark</item>
        <item name="colorOnBackground">@color/orange_text_primary_dark</item>
        <item name="colorOnSurface">@color/orange_text_primary_dark</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/orange_background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/orange_background_dark</item>
        
        <!-- Action bar -->
        <item name="actionBarStyle">@style/OrangeActionBarDark</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/orange_text_primary_dark</item>
        <item name="android:textColorSecondary">@color/orange_text_secondary_dark</item>
        
        <!-- Button style -->
        <item name="materialButtonStyle">@style/OrangeButtonDark</item>
        
        <!-- Card style -->
        <item name="materialCardViewStyle">@style/OrangeCardDark</item>
    </style>

    <!-- Orange Action Bar Dark Style -->
    <style name="OrangeActionBarDark" parent="Widget.Material3.ActionBar.Solid">
        <item name="android:background">@color/orange_surface_dark</item>
        <item name="android:titleTextStyle">@style/OrangeActionBarTitleDark</item>
        <item name="elevation">4dp</item>
    </style>

    <!-- Orange Action Bar Title Dark -->
    <style name="OrangeActionBarTitleDark" parent="TextAppearance.Material3.ActionBar.Title">
        <item name="android:textColor">@color/orange_text_primary_dark</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Orange Button Dark Style -->
    <style name="OrangeButtonDark" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/orange_primary_light</item>
        <item name="android:textColor">@color/black</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <!-- Orange Card Dark Style -->
    <style name="OrangeCardDark" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/orange_surface_dark</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
</resources>
