package com.orangeui.android.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// Orange UI Color Palette
val OrangePrimary = Color(0xFFFF6B35)
val OrangePrimaryDark = Color(0xFFE55A2B)
val OrangePrimaryLight = Color(0xFFFF8A65)

val OrangeSecondary = Color(0xFFFF9500)
val OrangeSecondaryDark = Color(0xFFE6850E)
val OrangeSecondaryLight = Color(0xFFFFB74D)

val OrangeAccent = Color(0xFFFF4500)
val OrangeAccentLight = Color(0xFFFF7043)

val OrangeBackground = Color(0xFFFFF8F5)
val OrangeBackgroundDark = Color(0xFF2E1A0F)
val OrangeSurface = Color(0xFFFFFFFF)
val OrangeSurfaceDark = Color(0xFF1A1A1A)

val OrangeTextPrimary = Color(0xFF212121)
val OrangeTextSecondary = Color(0xFF757575)
val OrangeTextPrimaryDark = Color(0xFFFFFFFF)
val OrangeTextSecondaryDark = Color(0xFFBDBDBD)

val OrangeSuccess = Color(0xFF4CAF50)
val OrangeWarning = Color(0xFFFF9800)
val OrangeError = Color(0xFFF44336)
val OrangeInfo = Color(0xFF2196F3)

private val DarkColorScheme = darkColorScheme(
    primary = OrangePrimaryLight,
    onPrimary = Color.Black,
    primaryContainer = OrangePrimary,
    onPrimaryContainer = Color.White,
    secondary = OrangeSecondaryLight,
    onSecondary = Color.Black,
    secondaryContainer = OrangeSecondary,
    onSecondaryContainer = Color.White,
    tertiary = OrangeAccentLight,
    onTertiary = Color.Black,
    background = OrangeBackgroundDark,
    onBackground = OrangeTextPrimaryDark,
    surface = OrangeSurfaceDark,
    onSurface = OrangeTextPrimaryDark,
    surfaceVariant = OrangeSurfaceDark,
    onSurfaceVariant = OrangeTextSecondaryDark,
    error = OrangeError,
    onError = Color.White,
    outline = OrangeTextSecondaryDark
)

private val LightColorScheme = lightColorScheme(
    primary = OrangePrimary,
    onPrimary = Color.White,
    primaryContainer = OrangePrimaryLight,
    onPrimaryContainer = OrangePrimaryDark,
    secondary = OrangeSecondary,
    onSecondary = Color.White,
    secondaryContainer = OrangeSecondaryLight,
    onSecondaryContainer = OrangeSecondaryDark,
    tertiary = OrangeAccent,
    onTertiary = Color.White,
    background = OrangeBackground,
    onBackground = OrangeTextPrimary,
    surface = OrangeSurface,
    onSurface = OrangeTextPrimary,
    surfaceVariant = OrangeSurface,
    onSurfaceVariant = OrangeTextSecondary,
    error = OrangeError,
    onError = Color.White,
    outline = OrangeTextSecondary
)

@Composable
fun OrangeUITheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // Disabled to maintain Orange UI branding
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
