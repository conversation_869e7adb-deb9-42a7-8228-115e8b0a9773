<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Orange UI Light Theme -->
    <style name="Theme.OrangeUI" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/orange_primary</item>
        <item name="colorPrimaryVariant">@color/orange_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/orange_secondary</item>
        <item name="colorSecondaryVariant">@color/orange_secondary_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/orange_background</item>
        <item name="colorSurface">@color/orange_surface</item>
        <item name="colorOnBackground">@color/orange_text_primary</item>
        <item name="colorOnSurface">@color/orange_text_primary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/orange_primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/orange_primary</item>
        
        <!-- Action bar -->
        <item name="actionBarStyle">@style/OrangeActionBar</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/orange_text_primary</item>
        <item name="android:textColorSecondary">@color/orange_text_secondary</item>
        
        <!-- Button style -->
        <item name="materialButtonStyle">@style/OrangeButton</item>
        
        <!-- Card style -->
        <item name="materialCardViewStyle">@style/OrangeCard</item>
    </style>

    <!-- Orange Action Bar Style -->
    <style name="OrangeActionBar" parent="Widget.Material3.ActionBar.Solid">
        <item name="android:background">@color/orange_primary</item>
        <item name="android:titleTextStyle">@style/OrangeActionBarTitle</item>
        <item name="elevation">4dp</item>
    </style>

    <!-- Orange Action Bar Title -->
    <style name="OrangeActionBarTitle" parent="TextAppearance.Material3.ActionBar.Title">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Orange Button Style -->
    <style name="OrangeButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/orange_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <!-- Orange Card Style -->
    <style name="OrangeCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/orange_surface</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Orange Text Styles -->
    <style name="OrangeHeadline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/orange_text_primary</item>
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="OrangeHeadline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/orange_text_primary</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="OrangeBody1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/orange_text_primary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="OrangeBody2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/orange_text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style>
</resources>
